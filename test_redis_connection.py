#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis连接测试脚本
用于验证Redis连接池配置和重连机制是否正常工作
"""

import asyncio
import json
from utils.redis_util import get_redis_pool, get_redis_instance
from redis import asyncio as aioredis

async def test_redis_connection():
    """测试Redis连接"""
    print("开始测试Redis连接...")
    
    try:
        # 测试连接池
        pool = get_redis_pool()
        redis_client = aioredis.Redis(connection_pool=pool)
        
        # 测试ping
        result = await redis_client.ping()
        print(f"Redis ping 测试: {result}")
        
        # 测试发布消息
        test_message = {
            "type": "private_message",
            "msg": "测试消息",
            "sender": "test_user_1",
            "recipient": "test_user_2"
        }
        
        await redis_client.publish('chat', json.dumps(test_message))
        print("测试消息发布成功")
        
        return True
        
    except Exception as e:
        print(f"Redis连接测试失败: {e}")
        return False

async def test_redis_subscription():
    """测试Redis订阅功能"""
    print("开始测试Redis订阅...")
    
    try:
        pool = get_redis_pool()
        redis_client = aioredis.Redis(connection_pool=pool)
        
        # 创建订阅
        psub = redis_client.pubsub()
        await psub.subscribe("chat")
        print("订阅chat频道成功")
        
        # 监听消息（测试5秒）
        timeout = 5
        start_time = asyncio.get_event_loop().time()
        
        async for message in psub.listen():
            current_time = asyncio.get_event_loop().time()
            if current_time - start_time > timeout:
                break
                
            if message["type"] == "message":
                print(f"收到消息: {message['data']}")
                
        await psub.unsubscribe("chat")
        await psub.close()
        print("订阅测试完成")
        
        return True
        
    except Exception as e:
        print(f"Redis订阅测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("=" * 50)
    print("Redis连接和订阅测试")
    print("=" * 50)
    
    # 测试基本连接
    connection_ok = await test_redis_connection()
    
    if connection_ok:
        print("\n" + "-" * 30)
        # 测试订阅功能
        subscription_ok = await test_redis_subscription()
        
        if subscription_ok:
            print("\n✅ 所有测试通过！Redis连接和订阅功能正常")
        else:
            print("\n❌ 订阅测试失败")
    else:
        print("\n❌ 连接测试失败")
    
    print("=" * 50)

if __name__ == "__main__":
    asyncio.run(main())
