from typing import Dict, List, Optional
import json
import time

class RedisMessageQueue:
    """Redis消息队列管理类"""

    # 键名前缀常量，便于统一管理和修改
    OFFLINE_MSG_PREFIX = "offline_msgs:"
    UNREAD_COUNT_PREFIX = "unread_counts:"

    @staticmethod
    async def add_offline_message(redis, recipient_id: str, message_id: int, chatroom_id: int):
        """添加离线消息到队列"""
        timestamp = int(time.time() * 1000)
        message_data = json.dumps({"message_id": message_id, "chatroom_id": chatroom_id})

        # 使用管道批量执行操作，提高性能
        async with redis.pipeline() as pipe:
            # 添加到用户的离线消息队列
            await pipe.zadd(f"{RedisMessageQueue.OFFLINE_MSG_PREFIX}{recipient_id}", {message_data: timestamp})
            # 更新未读消息计数
            await pipe.hincrby(f"{RedisMessageQueue.UNREAD_COUNT_PREFIX}{recipient_id}", str(chatroom_id), 1)
            await pipe.execute()

    @staticmethod
    async def get_offline_messages(redis, user_id: str, limit: int = 100) -> List[Dict]:
        """获取用户的离线消息"""
        # 获取所有离线消息，按时间戳排序
        messages = await redis.zrange(
            f"{RedisMessageQueue.OFFLINE_MSG_PREFIX}{user_id}",
            0, -1,
            withscores=True
        )

        result = []
        for msg_data, timestamp in messages:
            try:
                msg_obj = json.loads(msg_data)
                result.append({
                    "message_id": msg_obj["message_id"],
                    "chatroom_id": msg_obj["chatroom_id"],
                    "timestamp": timestamp
                })
            except json.JSONDecodeError:
                # 忽略无效的JSON数据
                continue

        return result[:limit]

    @staticmethod
    async def remove_offline_message(redis, user_id: str, message_id: int):
        """从队列中移除已发送的离线消息"""
        # 获取所有离线消息
        messages = await redis.zrange(f"{RedisMessageQueue.OFFLINE_MSG_PREFIX}{user_id}", 0, -1)

        # 查找并删除指定消息ID的消息
        for msg_data in messages:
            try:
                msg_obj = json.loads(msg_data)
                if msg_obj["message_id"] == message_id:
                    await redis.zrem(f"{RedisMessageQueue.OFFLINE_MSG_PREFIX}{user_id}", msg_data)
                    break
            except json.JSONDecodeError:
                # 忽略无效的JSON数据
                continue

    @staticmethod
    async def remove_offline_messages_batch(redis, user_id: str, message_ids: List[int]):
        """批量移除已发送的离线消息 - 高性能版本"""
        if not message_ids:
            return

        # 获取所有离线消息
        messages = await redis.zrange(f"{RedisMessageQueue.OFFLINE_MSG_PREFIX}{user_id}", 0, -1)

        # 收集需要删除的消息数据
        to_remove = []
        message_ids_set = set(message_ids)

        for msg_data in messages:
            try:
                msg_obj = json.loads(msg_data)
                if msg_obj["message_id"] in message_ids_set:
                    to_remove.append(msg_data)
            except json.JSONDecodeError:
                continue

        # 批量删除
        if to_remove:
            async with redis.pipeline() as pipe:
                for msg_data in to_remove:
                    await pipe.zrem(f"{RedisMessageQueue.OFFLINE_MSG_PREFIX}{user_id}", msg_data)
                await pipe.execute()

    @staticmethod
    async def clear_all_offline_messages(redis, user_id: str):
        """清空用户的所有离线消息 - 最高性能版本"""
        await redis.delete(f"{RedisMessageQueue.OFFLINE_MSG_PREFIX}{user_id}")

    @staticmethod
    async def get_unread_counts(redis, user_id: str) -> Dict[str, int]:
        """获取用户的未读消息计数"""
        counts = await redis.hgetall(f"{RedisMessageQueue.UNREAD_COUNT_PREFIX}{user_id}")
        return {k: int(v) for k, v in counts.items()}

    @staticmethod
    async def reset_unread_count(redis, user_id: str, chatroom_id: int):
        """重置特定聊天室的未读计数"""
        await redis.hset(f"{RedisMessageQueue.UNREAD_COUNT_PREFIX}{user_id}", str(chatroom_id), 0)

    @staticmethod
    async def clear_user_data(redis, user_id: str):
        """清除用户的所有队列数据，用于用户注销或数据清理"""
        async with redis.pipeline() as pipe:
            await pipe.delete(f"{RedisMessageQueue.OFFLINE_MSG_PREFIX}{user_id}")
            await pipe.delete(f"{RedisMessageQueue.UNREAD_COUNT_PREFIX}{user_id}")
            await pipe.execute()
