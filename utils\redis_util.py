from redis import asyncio as aioredis
from redis.asyncio.connection import ConnectionPool
from redis.exceptions import ConnectionError, TimeoutError
from config.get_config import config

# Redis配置
host = config['db_redis']['host']
db = config['db_redis']['db']
timeout = config['db_redis']['timeout']
password = config['db_redis']['password']
port = config['db_redis']['port']

# 全局Redis连接池和实例
_redis_pool = None
_redis_instance = None

def get_redis_pool():
    """获取Redis连接池"""
    global _redis_pool
    if _redis_pool is None:
        _redis_pool = ConnectionPool(
            host=host,
            port=port,
            db=db,
            password=password,
            encoding="utf-8",
            decode_responses=True,
            max_connections=50,      # 最大连接数
            retry_on_timeout=True,   # 超时重试
            retry_on_error=[ConnectionError, TimeoutError],  # 连接错误时重试
            socket_keepalive=True,   # 保持连接活跃
            socket_keepalive_options={
                1: 1,  # TCP_KEEPIDLE
                2: 3,  # TCP_KEEPINTVL
                3: 5,  # TCP_KEEPCNT
            },
            health_check_interval=30,  # 健康检查间隔（秒）
            socket_connect_timeout=5,  # 连接超时
            socket_timeout=5,          # 读写超时
        )
    return _redis_pool

async def get_redis():
    """获取Redis连接（作为依赖项使用）"""
    pool = get_redis_pool()
    redis = aioredis.Redis(connection_pool=pool)
    try:
        yield redis
    finally:
        # 连接会自动返回到连接池，无需手动关闭
        pass

async def get_redis_instance():
    """获取Redis连接实例（直接调用使用）"""
    global _redis_instance
    if _redis_instance is None:
        pool = get_redis_pool()
        _redis_instance = aioredis.Redis(connection_pool=pool)
    return _redis_instance

async def close_redis_pool():
    """关闭Redis连接池（应用关闭时调用）"""
    global _redis_pool, _redis_instance
    if _redis_instance:
        await _redis_instance.close()
        _redis_instance = None
    if _redis_pool:
        await _redis_pool.disconnect()
        _redis_pool = None
