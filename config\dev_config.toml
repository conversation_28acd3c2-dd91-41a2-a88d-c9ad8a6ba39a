[log]
log_path = "/log/py/ws_runtime_{time:YYYY-MM}.log"
rotation = "1 month"
format = "{time:YYYY-MM-DD HH:mm:ss} |  {level}      | {name} : {function} : {line}  -  {message}"
retention = '60 days'

[db_redis]
host = "r-bp1lpo0xujesrdk2wrpd.redis.rds.aliyuncs.com"
port = 6379
db = 16
password = "cS6Nm$CxZpMl!5#U"
timeout = 60000


[db_mysql]
host = "rm-bp1gsw85wvz6x8edc1o.mysql.rds.aliyuncs.com"
port = 3306
user = "mfcad_test"
psd = "Y8bR6A3t4zBzPgpJ"
database = "zb_mfcad_com"


[jwt]
secret_key = "Y5fSqhxiR51z2D2ppkIYbl8IaJ3mdVrw"
algorithm = "HS256"
access_token_expire_minutes = 1440  # 24小时
