#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多进程Redis订阅测试脚本
模拟gunicorn多进程环境下的Redis订阅行为
"""

import asyncio
import multiprocessing
import os
import signal
import time
from main import RedisPubSubManager
import json

class ProcessTester:
    def __init__(self, process_id):
        self.process_id = process_id
        self.running = True
        
    async def simulate_redis_subscription(self):
        """模拟Redis订阅过程"""
        print(f"进程 {self.process_id} (PID: {os.getpid()}) 开始Redis订阅测试")
        
        # 创建Redis订阅管理器
        manager = RedisPubSubManager()
        
        try:
            # 启动订阅
            await manager.start_subscription()
        except Exception as e:
            print(f"进程 {self.process_id} Redis订阅出错: {e}")
        finally:
            manager.stop_subscription()
            print(f"进程 {self.process_id} Redis订阅已停止")

def worker_process(process_id):
    """工作进程函数"""
    def signal_handler(signum, frame):
        print(f"进程 {process_id} 收到信号 {signum}，准备退出")
        exit(0)
    
    # 注册信号处理器
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # 创建测试器
        tester = ProcessTester(process_id)
        
        # 运行异步测试
        asyncio.run(tester.simulate_redis_subscription())
        
    except KeyboardInterrupt:
        print(f"进程 {process_id} 被中断")
    except Exception as e:
        print(f"进程 {process_id} 出现异常: {e}")

def main():
    """主函数 - 模拟多进程部署"""
    print("开始多进程Redis订阅测试")
    print("=" * 50)
    
    # 模拟gunicorn的worker数量
    worker_count = 4
    processes = []
    
    try:
        # 启动多个工作进程
        for i in range(worker_count):
            process = multiprocessing.Process(
                target=worker_process, 
                args=(i + 1,)
            )
            process.start()
            processes.append(process)
            print(f"启动工作进程 {i + 1} (PID: {process.pid})")
        
        print(f"\n所有 {worker_count} 个工作进程已启动")
        print("测试将运行30秒...")
        
        # 让进程运行一段时间
        time.sleep(30)
        
    except KeyboardInterrupt:
        print("\n收到中断信号，正在停止所有进程...")
    
    finally:
        # 停止所有进程
        for i, process in enumerate(processes):
            if process.is_alive():
                print(f"正在停止进程 {i + 1}...")
                process.terminate()
                process.join(timeout=5)
                
                if process.is_alive():
                    print(f"强制杀死进程 {i + 1}")
                    process.kill()
                    process.join()
        
        print("所有进程已停止")
        print("=" * 50)

if __name__ == "__main__":
    # 设置多进程启动方法
    multiprocessing.set_start_method('spawn', force=True)
    main()
