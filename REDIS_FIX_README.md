# Redis多进程订阅修复说明

## 问题描述

在使用gunicorn多进程部署FastAPI WebSocket聊天应用时，Redis发布/订阅机制出现连接断开问题：

- 单进程运行正常
- 多进程部署时频繁出现 "Connection closed by server" 错误
- Redis订阅连接断开后无法自动重连
- 影响多进程间的实时消息同步

## 问题根因

1. **连接池配置不适合多进程环境**：缺少重连机制和错误处理
2. **订阅连接缺乏健壮性**：连接断开后无法自动恢复
3. **资源清理不完善**：进程重启时可能导致连接泄漏

## 解决方案

### 1. 优化Redis连接池配置 (`utils/redis_util.py`)

```python
# 增加的配置项
retry_on_error=[ConnectionError, TimeoutError]  # 连接错误时重试
socket_keepalive_options={
    1: 1,  # TCP_KEEPIDLE
    2: 3,  # TCP_KEEPINTVL  
    3: 5,  # TCP_KEEPCNT
}
socket_connect_timeout=5  # 连接超时
socket_timeout=5          # 读写超时
```

### 2. 实现Redis订阅管理器 (`main.py`)

新增 `RedisPubSubManager` 类，提供：

- **自动重连机制**：连接断开时自动重试
- **指数退避策略**：避免频繁重连造成服务器压力
- **优雅停止**：应用关闭时正确清理资源
- **错误隔离**：单个消息处理错误不影响整个订阅

### 3. 关键特性

#### 自动重连
- 最大重试次数：10次
- 基础重试延迟：2秒
- 最大重试延迟：60秒
- 指数退避算法：`retry_delay = min(retry_delay * 1.5, 60)`

#### 连接健康检查
- 订阅前执行 `ping()` 测试
- 连接池健康检查间隔：30秒
- TCP keepalive 保持连接活跃

#### 资源管理
- 进程启动时创建订阅管理器
- 进程关闭时优雅停止订阅
- 自动清理PubSub资源

## 修改文件

### 1. `utils/redis_util.py`
- 增加连接错误重试配置
- 优化TCP keepalive设置
- 添加连接和读写超时

### 2. `main.py`
- 新增 `RedisPubSubManager` 类
- 修改 `register_pubsub()` 函数
- 优化应用启动和关闭事件处理

## 部署建议

### 1. Gunicorn配置优化

```python
# config/fastapi_config.py
workers = 4  # 根据CPU核心数调整
worker_class = "uvicorn.workers.UvicornWorker"
timeout = 30
keepalive = 2
```

### 2. Redis配置优化

```ini
# redis.conf
tcp-keepalive 300
timeout 0
tcp-backlog 511
```

### 3. 监控建议

- 监控Redis连接数
- 监控订阅重连频率
- 监控消息处理延迟

## 测试验证

### 1. 基础连接测试
```bash
python test_redis_connection.py
```

### 2. 多进程测试
```bash
python test_multiprocess.py
```

### 3. 生产环境测试
```bash
gunicorn -c config/fastapi_config.py main:app
```

## 性能影响

- **最小化代码改动**：保持现有API不变
- **连接复用**：使用连接池避免频繁创建连接
- **异步处理**：不阻塞主要业务逻辑
- **内存优化**：及时清理断开的连接

## 监控指标

建议监控以下指标：
- Redis连接数
- 订阅重连次数
- 消息处理成功率
- 连接断开频率

## 故障排查

### 常见问题

1. **连接超时**：检查网络和Redis服务器状态
2. **认证失败**：验证Redis密码配置
3. **内存不足**：检查Redis内存使用情况
4. **端口冲突**：确认Redis端口可访问

### 日志关键字

- "已订阅Redis频道: chat" - 订阅成功
- "Redis订阅出错" - 连接问题
- "将在 X 秒后重试" - 自动重连
- "Redis订阅管理器已停止" - 正常关闭

## 总结

通过实现健壮的Redis连接管理和自动重连机制，解决了多进程部署时的订阅连接问题，确保了：

1. **高可用性**：自动重连保证服务连续性
2. **高性能**：连接池复用减少开销
3. **易维护**：最小化代码改动
4. **可监控**：详细的日志和错误处理
